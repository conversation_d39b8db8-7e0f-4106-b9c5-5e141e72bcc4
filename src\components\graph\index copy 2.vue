<template>
  <div class="wrap">
    <div id="graph-container" class="graph"></div>

    <div class="toolbar">
      <button @click="switchLayout('tree')">垂直布局</button>
      <button @click="switchLayout('dendrogram')">生态树布局</button>
      <button @click="switchLayout('radial')">生态图布局</button>
    </div>
    <!-- 右上角行动信息面板 -->
    <div class="action-panel">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="行动信息" name="action">
          <div class="action-info">
            <div class="info-item" v-for="item in actionInfo" :key="item.key">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value || "暂无" }}</span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 信息面板：点击节点后出现 -->
    <div v-if="panel.visible" class="info-panel">
      <div class="panel-hd">
        <div class="title">{{ panel.title }}</div>
        <div class="sub">{{ panel.sub }}</div>
        <button class="close" @click="panel.visible = false">×</button>
      </div>
      <div class="panel-bd">
        <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
          <div class="k">{{ r.name ?? r.code }}</div>
          <div class="v">{{ r.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  actionInfo: {
    type: Array,
    default: () => [
      { label: "行动名称", value: "", key: "name" },
      { label: "开始时间", value: "", key: "startTime" },
      { label: "任务时长", value: "", key: "duration" },
      { label: "作战方向", value: "", key: "zzfsType" },
      { label: "区域范围", value: "", key: "position" },
      { label: "行动描述", value: "", key: "description" },
    ],
  },
});

const data = reactive(props.data);

/* ================== 数据：用你给的 data 原样放这儿 ================== */
// // 添加行动信息相关状态
const activeCollapse = ref(["action"]); // 默认展开

/* ================== 面板状态 ================== */
const panel = reactive({
  visible: false,
  title: "",
  sub: "",
  attrs: [],
});

/* ================== 颜色：固定 + 自动补齐（最多6种） ================== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" }, // 示例已知
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];

/* ================== 通用树构造（只看 links 方向） ================== */
/**
 * 通用版：仅根据 links 构造森林（children-only 结构）。
 * - link.source → link.target 当作 父→子；如果有关系需要反向，可在 options.reverseByRelation 里写名字
 * - 多父会为 child 按“父亲上下文”克隆（id 带 @p:<parentId>）
 * - 检测环并截断（避免死循环）
 */
function buildForestFromLinks(data, options = {}) {
  const { reverseByRelation = new Set(), maxDepth = 16 } = options;

  const nodes = data.nodes || [];
  const links = data.links || [];
  const nodeById = new Map(nodes.map((n) => [String(n.id), n]));

  const getTypeKey = (n) => n?.labels?.[0] || n?.label || "other";
  const getAttr = (n, code) => n?.attrs?.find((a) => a.code === code || a.name === code)?.value ?? "";
  const getName = (n) => n?.nodeName || getAttr(n, "name") || n?.entityId || n?.id || "";

  const makeId = (type, id, ctx) => (ctx ? `${type}:${id}@${ctx}` : `${type}:${id}`);
  const toTreeNode = (n, type, ctx, parentRelation) => ({
    id: makeId(type, String(n.id), ctx),
    name: getName(n),
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type,
      attrs: n.attrs,
      parentRelation: parentRelation || "",
    },
  });

  // 出边 + 入度
  const out = new Map(); // id -> [{ to, relation }]
  const indeg = new Map(); // id -> number
  for (const e of links) {
    const rel = e.name || "";
    let s = String(e.source);
    let t = String(e.target);
    if (reverseByRelation.has(rel)) {
      const tmp = s;
      s = t;
      t = tmp;
    }
    if (!out.has(s)) out.set(s, []);
    out.get(s).push({ to: t, relation: rel });
    indeg.set(t, (indeg.get(t) || 0) + 1);
  }

  const allIds = nodes.map((n) => String(n.id));
  const rootIds = allIds.filter((id) => !indeg.get(id));

  function dfs(curId, ctx, depth, stack) {
    const n = nodeById.get(curId);
    if (!n) return null;
    const type = getTypeKey(n);
    const node = toTreeNode(n, type, ctx, stack?.parentRelation);
    if (depth >= maxDepth) return node;

    const seen = stack?.set || new Set();
    if (seen.has(curId)) return node; // 环，截断

    const nextSeen = new Set(seen);
    nextSeen.add(curId);

    const children = [];
    const outs = out.get(curId) || [];
    for (const e of outs) {
      const childCtx = `p:${curId}`;
      const child = dfs(e.to, childCtx, depth + 1, {
        set: nextSeen,
        parentRelation: e.relation,
      });
      if (child) children.push(child);
    }
    if (children.length) node.children = children;
    return node;
  }

  const forest = [];
  if (rootIds.length) {
    for (const rid of rootIds) {
      const sub = dfs(rid, "", 0, { set: new Set(), parentRelation: "" });
      if (sub) forest.push(sub);
    }
    return forest;
  }

  // 没有根（全环）→ 虚拟根
  const virtualChildren = [];
  for (const id of allIds) {
    const sub = dfs(id, "root", 0, { set: new Set(), parentRelation: "" });
    if (sub) virtualChildren.push(sub);
  }
  return [{ id: "ROOT", name: "ROOT", data: { type: "other" }, children: virtualChildren }];
}

/* ================== 边样式 ================== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelAutoRotate: false, // 性能更稳
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};

/* ================== 叶子判断（仅用于标签朝向） ================== */
function isLeafNode(d) {
  return !d?.children || d.children.length === 0;
}

/* ================== 图实例 & 布局切换 ================== */
const graphRef = ref(null);

const switchLayout = (type) => {
  const g = graphRef.value;
  if (!g) return;
  if (type === "tree") {
    g.setLayout({ type: "dendrogram", direction: "TB", nodeSep: 150, rankSep: 200 });
    g.setEdge({ type: "cubic-vertical", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "dendrogram") {
    g.setLayout({ type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "cubic-horizontal", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "radial") {
    g.setLayout({ type: "dendrogram", radial: true, nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "line", style: EDGE_STYLE, animation: { enter: false } });
  }
  g.layout();
  g.fitView(24);
};

onMounted(() => {
  /* 1) 用“泛化关系”构造森林 & 根 */
  const forests = buildForestFromLinks(data /*, { reverseByRelation: new Set(['如果有要反向的关系名放这里']) } */);
  const treeRoot = forests.length === 1 ? forests[0] : { id: "ROOT", name: "ROOT", children: forests };

  /* 2) 生成 graphData，并把真实关系名写到每条边的 data.relation */
  const gData = treeToGraphData(treeRoot);
  const nodeIndex = new Map((gData.nodes || []).map((n) => [String(n.id), n]));
  gData.edges = (gData.edges || []).map((e) => {
    const relation = nodeIndex.get(String(e.target))?.data?.parentRelation || "";
    return { ...e, data: { ...(e.data || {}), relation } };
  });

  /* 3) 自动扩展颜色（最多 6 种），优先用你手动定义的 */
  const ALL_TYPES = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const COLOR_AUTO = {};
  let i = 0;
  for (const t of ALL_TYPES) {
    COLOR_AUTO[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  }

  /* 4) 创建图 */
  const g = (graphRef.value = new Graph({
    container: "graph-container",
    autoFit: "view",
    data: gData,
    node: {
      type: "rect",
      style: (d) => {
        const t = d?.data?.type ?? "other";
        const { fill, stroke } = COLOR_AUTO[t] || COLOR_BY_TYPE.other;
        return {
          fill,
          stroke,
          lineWidth: 1.5,
          radius: 8,
          padding: [4, 8, 4, 8],
          labelText: d.name ?? d.id,
          labelFill: "#fff",
          labelPlacement: isLeafNode(d) ? "right" : "left",
          labelBackground: true,
          draggable: false, // 强制不可拖拽节点
        };
      },
      animation: { enter: false },
    },

    edge: {
      type: "cubic-horizontal",
      style: EDGE_STYLE,
      animation: { enter: false },
    },
    layout: { type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 },
    // 只开画布拖拽与缩放
    behaviors: [
      { type: "drag-canvas", enable: true },
      { type: "zoom-canvas", enable: true },
    ],
    // 悬浮 Tooltip（预览属性前几条）
    plugins: [
      {
        type: "tooltip",
        key: "node-tooltip",
        itemTypes: ["node"],
        trigger: "click",
        getContent: (e) => {
          // 通过图实例获取完整的节点数据
          if (e.targetType !== "node") return "";
          const nodeId = e?.target?.id;
          if (!nodeId) return "";
          const nodeData = g.getNodeData(nodeId);
          const attrs = nodeData?.data?.attrs || [];
          const top = attrs
            // .slice(0, 6)
            .map((a) => `<div class="row"><b>${a.name ?? a.code}</b>：${a.value ?? ""}</div>`)
            .join("");
          return `<div class="g6-tip"><div class="hd">${nodeData?.name ?? nodeId}</div>${top || "<i>无属性</i>"}</div>`;
        },
        shouldBegin: (e) => e?.target?.type === "node",
        offsetX: 12,
        offsetY: 8,
      },
      {
        type: "fullscreen",
        key: "fullscreen",
      },
      {
        type: "minimap",
        size: [240, 160],
      },
      function () {
        const graph = this;
        return {
          type: "toolbar",
          key: "toolbar",
          position: "bottom-left",
          onClick: (item) => {
            const fullscreenPlugin = graph.getPluginInstance("fullscreen");
            if (item === "request-fullscreen") {
              fullscreenPlugin.request();
            }
            if (item === "exit-fullscreen") {
              fullscreenPlugin.exit();
            }
          },
          getItems: () => {
            return [
              { id: "request-fullscreen", value: "request-fullscreen" },
              { id: "exit-fullscreen", value: "exit-fullscreen" },
            ];
          },
        };
      },
    ],
    theme: "dark",
  }));

  g.render();
});

onBeforeUnmount(() => {
  graphRef.value?.destroy();
  graphRef.value = null;
});
</script>

<style scoped>
.wrap {
  position: relative;
}
.toolbar {
  display: flex;
  gap: 8px;
  position: absolute;
  top: 0px;
  left: 0px;
}
.graph {
  width: 100%;
  height: 800px;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #9ecbff;
  word-break: break-all;
}
.v {
  color: #e8e8e8;
  word-break: break-all;
}

.g6-tip {
  max-width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border: 1px solid #333;
  padding: 8px 10px;
  border-radius: 6px;
}
.g6-tip .hd {
  font-weight: 700;
  margin-bottom: 6px;
}
.g6-tip .row {
  font-size: 12px;
  margin: 2px 0;
}

.action-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  border-radius: 8px;
  z-index: 100;
}

.action-info {
  padding: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
}

.label {
  color: #409eff;
  min-width: 70px;
  flex-shrink: 0;
}

.value {
  color: #111;
  flex: 1;
  word-break: break-all;
}

.action-panel :deep(.el-collapse-item__header) {
  padding: 12px 15px;
}

.action-panel :deep(.el-collapse-item__content) {
  padding: 0;
}
</style>
